
using Fantasy;
using Fantasy.Async;
using Fantasy.Network;
using Fantasy.Network.Interface;
using Fantasy.Platform.Net;

namespace Demo
{
    public class C2G_TestMessageHandler : Message<C2G_TestMessage>
    {
        protected override async FTask Run(Session session, C2G_TestMessage message)
        {
            Console.WriteLine($"Received client message: {message.Tag}");
            var chatSceneConfigs = SceneConfigData.Instance.GetSceneBySceneType(SceneType.Chat);
            var chatSceneConfig = chatSceneConfigs[0];
            session.Scene.NetworkMessagingComponent.SendInnerRoute(chatSceneConfig.RouteId, new G2Chat_TestMessage(){Tag = "Message, Server Chat!"});
            await FTask.CompletedTask;
        }
    }
}
